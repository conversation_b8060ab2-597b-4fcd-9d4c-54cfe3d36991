//package cc.unitmesh.devti.mcp.marketplace
//
//import cc.unitmesh.devti.mcp.marketplace.model.*
//import kotlinx.serialization.json.Json
//import kotlinx.serialization.encodeToString
//import kotlinx.serialization.decodeFromString
//import kotlinx.serialization.json.JsonObject
//import org.junit.Test
//import kotlin.test.assertEquals
//import kotlin.test.assertNotNull
//
///**
// * 序列化测试 - 验证所有数据模型都能正确序列化和反序列化
// */
//class SerializationTest {
//
//    private val json = Json {
//        ignoreUnknownKeys = true
//        prettyPrint = true
//    }
//
//    @Test
//    fun testMcpPackageSerialization() {
//        // Given
//        val mcpPackage = McpPackage(
//            id = "test-package",
//            name = "@test/package",
//            displayName = "Test Package",
//            description = "A test package for MCP",
//            version = "1.0.0",
//            author = "Test Author",
//            repository = "https://github.com/test/package",
//            license = "MIT",
//            keywords = listOf("test", "mcp"),
//            category = McpCategory.GENERAL,
//            installType = InstallType.NPX,
//            installCommand = "npx @test/package",
//            args = listOf("--test"),
//            env = mapOf("TEST_ENV" to "true"),
//            tools = listOf(
//                McpToolInfo("test_tool", "A test tool"),
//                McpToolInfo("another_tool", "Another test tool")
//            ),
//            rating = 4.5,
//            downloads = 1000,
//            lastUpdated = "2024-01-01",
//            isOfficial = false,
//            isVerified = true
//        )
//
//        // When
//        val jsonString = json.encodeToString(mcpPackage)
//        val decoded = json.decodeFromString<McpPackage>(jsonString)
//
//        // Then
//        assertNotNull(jsonString)
//        assertEquals(mcpPackage.id, decoded.id)
//        assertEquals(mcpPackage.name, decoded.name)
//        assertEquals(mcpPackage.displayName, decoded.displayName)
//        assertEquals(mcpPackage.version, decoded.version)
//        assertEquals(mcpPackage.tools.size, decoded.tools.size)
//        assertEquals(mcpPackage.tools[0].name, decoded.tools[0].name)
//
//        println("McpPackage serialization test passed")
//        println("JSON: $jsonString")
//    }
//
//    @Test
//    fun testMcpToolInfoSerialization() {
//        // Given
//        val toolInfo = McpToolInfo(
//            name = "test_tool",
//            description = "A test tool with parameters"
//        )
//
//        // When
//        val jsonString = json.encodeToString(toolInfo)
//        val decoded = json.decodeFromString<McpToolInfo>(jsonString)
//
//        // Then
//        assertEquals(toolInfo.name, decoded.name)
//        assertEquals(toolInfo.description, decoded.description)
//        assertNotNull(decoded.parameters)
//
//        println("McpToolInfo serialization test passed")
//        println("JSON: $jsonString")
//    }
//
//    @Test
//    fun testMarketplaceResponseSerialization() {
//        // Given
//        val mcpPackage = McpPackage(
//            id = "test",
//            name = "test",
//            displayName = "Test",
//            description = "Test package",
//            version = "1.0.0",
//            author = "Test Author",
//            installCommand = "npx test"
//        )
//
//        val response = MarketplaceResponse(
//            packages = listOf(mcpPackage),
//            total = 1,
//            page = 1,
//            pageSize = 10,
//            hasMore = false
//        )
//
//        // When
//        val jsonString = json.encodeToString(response)
//        val decoded = json.decodeFromString<MarketplaceResponse>(jsonString)
//
//        // Then
//        assertEquals(response.total, decoded.total)
//        assertEquals(response.packages.size, decoded.packages.size)
//        assertEquals(response.packages[0].id, decoded.packages[0].id)
//
//        println("MarketplaceResponse serialization test passed")
//        println("JSON: $jsonString")
//    }
//
//    @Test
//    fun testMcpPackageInstallationSerialization() {
//        // Given
//        val installation = McpPackageInstallation(
//            packageId = "test-package",
//            installedVersion = "1.0.0",
//            installPath = "/path/to/package",
//            installDate = "2024-01-01",
//            status = InstallStatus.INSTALLED,
//            configName = "test-config",
//            isEnabled = true
//        )
//
//        // When
//        val jsonString = json.encodeToString(installation)
//        val decoded = json.decodeFromString<McpPackageInstallation>(jsonString)
//
//        // Then
//        assertEquals(installation.packageId, decoded.packageId)
//        assertEquals(installation.installedVersion, decoded.installedVersion)
//        assertEquals(installation.status, decoded.status)
//
//        println("McpPackageInstallation serialization test passed")
//        println("JSON: $jsonString")
//    }
//
//    @Test
//    fun testMarketplaceFilterSerialization() {
//        // Given
//        val filter = MarketplaceFilter(
//            query = "test",
//            category = McpCategory.DEVELOPMENT,
//            installType = InstallType.NPX,
//            isOfficial = true,
//            isVerified = true,
//            sortBy = SortBy.RATING
//        )
//
//        // When
//        val jsonString = json.encodeToString(filter)
//        val decoded = json.decodeFromString<MarketplaceFilter>(jsonString)
//
//        // Then
//        assertEquals(filter.query, decoded.query)
//        assertEquals(filter.category, decoded.category)
//        assertEquals(filter.installType, decoded.installType)
//        assertEquals(filter.sortBy, decoded.sortBy)
//
//        println("MarketplaceFilter serialization test passed")
//        println("JSON: $jsonString")
//    }
//
//    @Test
//    fun testAllEnumsSerialization() {
//        // Test McpCategory
//        McpCategory.values().forEach { category ->
//            val jsonString = json.encodeToString(category)
//            val decoded = json.decodeFromString<McpCategory>(jsonString)
//            assertEquals(category, decoded)
//        }
//
//        // Test InstallType
//        InstallType.values().forEach { installType ->
//            val jsonString = json.encodeToString(installType)
//            val decoded = json.decodeFromString<InstallType>(jsonString)
//            assertEquals(installType, decoded)
//        }
//
//        // Test SortBy
//        SortBy.values().forEach { sortBy ->
//            val jsonString = json.encodeToString(sortBy)
//            val decoded = json.decodeFromString<SortBy>(jsonString)
//            assertEquals(sortBy, decoded)
//        }
//
//        // Test InstallStatus
//        InstallStatus.values().forEach { status ->
//            val jsonString = json.encodeToString(status)
//            val decoded = json.decodeFromString<InstallStatus>(jsonString)
//            assertEquals(status, decoded)
//        }
//
//        println("All enums serialization test passed")
//    }
//
//    @Test
//    fun testComplexMcpPackageWithAllFields() {
//        // Given - 创建一个包含所有字段的复杂包
//        val mcpPackage = McpPackage(
//            id = "complex-package",
//            name = "@complex/package",
//            displayName = "Complex Package",
//            description = "A complex package with all fields filled",
//            version = "2.1.0",
//            author = "Complex Author",
//            repository = "https://github.com/complex/package",
//            homepage = "https://complex-package.dev",
//            license = "Apache-2.0",
//            keywords = listOf("complex", "full", "featured", "mcp"),
//            category = McpCategory.AI_ML,
//            installType = InstallType.DOCKER,
//            installCommand = "docker run complex/package",
//            args = listOf("--config", "/config", "--verbose"),
//            env = mapOf(
//                "API_KEY" to "secret",
//                "DEBUG" to "true",
//                "PORT" to "8080"
//            ),
//            requirements = listOf("docker", "python>=3.8"),
//            tools = listOf(
//                McpToolInfo("analyze", "Analyze data"),
//                McpToolInfo("predict", "Make predictions"),
//                McpToolInfo("train", "Train models")
//            ),
//            rating = 4.9,
//            downloads = 50000,
//            lastUpdated = "2024-01-20",
//            isOfficial = true,
//            isVerified = true,
//            screenshots = listOf(
//                "https://example.com/screenshot1.png",
//                "https://example.com/screenshot2.png"
//            ),
//            documentation = "https://docs.complex-package.dev",
//            changelog = "https://github.com/complex/package/releases"
//        )
//
//        // When
//        val jsonString = json.encodeToString(mcpPackage)
//        val decoded = json.decodeFromString<McpPackage>(jsonString)
//
//        // Then
//        assertEquals(mcpPackage.id, decoded.id)
//        assertEquals(mcpPackage.env.size, decoded.env.size)
//        assertEquals(mcpPackage.tools.size, decoded.tools.size)
//        assertEquals(mcpPackage.screenshots.size, decoded.screenshots.size)
//
//        println("Complex McpPackage serialization test passed")
//        println("Package has ${decoded.tools.size} tools and ${decoded.env.size} environment variables")
//    }
//}
