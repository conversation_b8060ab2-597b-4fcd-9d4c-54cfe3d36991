package cc.unitmesh.devti.mcp.marketplace.ui

import com.intellij.icons.AllIcons
import cc.unitmesh.devti.mcp.marketplace.model.*
import cc.unitmesh.devti.mcp.marketplace.service.SimpleMcpMarketplaceService
import cc.unitmesh.devti.mcp.marketplace.service.McpPackageInstaller
import cc.unitmesh.devti.mcp.marketplace.service.McpPackageInstallListener
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.ui.components.*
import com.intellij.ui.SearchTextField
import com.intellij.util.ui.JBUI
import com.intellij.util.ui.UIUtil
import java.util.concurrent.CompletableFuture
import java.awt.*
import java.awt.event.ActionEvent
import javax.swing.*

/**
 * MCP 市场对话框
 */
class McpMarketplaceDialog(private val project: Project) : DialogWrapper(project, true) {
    
    private val marketplaceService = SimpleMcpMarketplaceService.getInstance(project)
    private val packageInstaller = McpPackageInstaller.getInstance(project)
    
    private val searchField = SearchTextField()
    private val categoryCombo = JComboBox(McpCategory.values().map { it.displayName }.toTypedArray())
    private val typeCombo = JComboBox(InstallType.values().map { it.displayName }.toTypedArray())
    private val sortCombo = JComboBox(SortBy.values().map { it.displayName }.toTypedArray())
    private val officialCheckbox = JCheckBox("Official only")
    private val verifiedCheckbox = JCheckBox("Verified only")
    
    private val packagesPanel = JPanel()
    private val loadingPanel = JBLoadingPanel(BorderLayout(), project)
    private val scrollPane = JBScrollPane()
    
    private var currentPackages = listOf<McpPackage>()
    
    init {
        title = "MCP Marketplace"
        setSize(900, 700)
        init()
        loadInitialData()
        setupInstallListener()
    }
    
    override fun createCenterPanel(): JComponent {
        val mainPanel = JPanel(BorderLayout())
        mainPanel.preferredSize = Dimension(900, 700)
        
        // 搜索和过滤面板
        val filterPanel = createFilterPanel()
        mainPanel.add(filterPanel, BorderLayout.NORTH)
        
        // 包列表面板
        packagesPanel.layout = BoxLayout(packagesPanel, BoxLayout.Y_AXIS)
        scrollPane.setViewportView(packagesPanel)
        scrollPane.border = JBUI.Borders.empty()
        
        loadingPanel.add(scrollPane, BorderLayout.CENTER)
        mainPanel.add(loadingPanel, BorderLayout.CENTER)
        
        return mainPanel
    }
    
    override fun createActions(): Array<Action> {
        return arrayOf(cancelAction)
    }
    
    private fun createFilterPanel(): JComponent {
        val panel = JPanel(BorderLayout())
        panel.border = JBUI.Borders.empty(10)
        
        // 搜索栏
        val searchPanel = JPanel(BorderLayout())
        searchField.textEditor.emptyText.text = "Search packages..."
        searchField.addDocumentListener(object : javax.swing.event.DocumentListener {
            override fun insertUpdate(e: javax.swing.event.DocumentEvent?) = performSearch()
            override fun removeUpdate(e: javax.swing.event.DocumentEvent?) = performSearch()
            override fun changedUpdate(e: javax.swing.event.DocumentEvent?) = performSearch()
        })
        searchPanel.add(searchField, BorderLayout.CENTER)
        
        // 过滤器面板
        val filtersPanel = JPanel(FlowLayout(FlowLayout.LEFT))
        
        categoryCombo.insertItemAt("All Categories", 0)
        categoryCombo.selectedIndex = 0
        categoryCombo.addActionListener { performSearch() }
        
        typeCombo.insertItemAt("All Types", 0)
        typeCombo.selectedIndex = 0
        typeCombo.addActionListener { performSearch() }
        
        sortCombo.selectedIndex = 0
        sortCombo.addActionListener { performSearch() }
        
        officialCheckbox.addActionListener { performSearch() }
        verifiedCheckbox.addActionListener { performSearch() }
        
        filtersPanel.add(JLabel("Category:"))
        filtersPanel.add(categoryCombo)
        filtersPanel.add(Box.createHorizontalStrut(10))
        filtersPanel.add(JLabel("Type:"))
        filtersPanel.add(typeCombo)
        filtersPanel.add(Box.createHorizontalStrut(10))
        filtersPanel.add(JLabel("Sort:"))
        filtersPanel.add(sortCombo)
        filtersPanel.add(Box.createHorizontalStrut(10))
        filtersPanel.add(officialCheckbox)
        filtersPanel.add(verifiedCheckbox)
        
        panel.add(searchPanel, BorderLayout.NORTH)
        panel.add(filtersPanel, BorderLayout.SOUTH)
        
        return panel
    }
    
    private fun performSearch() {
        val filter = MarketplaceFilter(
            query = searchField.text.trim(),
            category = if (categoryCombo.selectedIndex > 0) McpCategory.values()[categoryCombo.selectedIndex - 1] else null,
            installType = if (typeCombo.selectedIndex > 0) InstallType.values()[typeCombo.selectedIndex - 1] else null,
            isOfficial = if (officialCheckbox.isSelected) true else null,
            isVerified = if (verifiedCheckbox.isSelected) true else null,
            sortBy = SortBy.values()[sortCombo.selectedIndex]
        )
        
        loadPackages(filter)
    }
    
    private fun loadInitialData() {
        loadPackages(MarketplaceFilter())
    }
    
    private fun loadPackages(filter: MarketplaceFilter) {
        loadingPanel.startLoading()

        val future = marketplaceService.searchPackages(filter)
        future.thenAccept { result ->
            result.fold(
                onSuccess = { response ->
                    currentPackages = response.packages
                    SwingUtilities.invokeLater {
                        updatePackagesDisplay()
                        loadingPanel.stopLoading()
                    }
                },
                onFailure = { error ->
                    SwingUtilities.invokeLater {
                        showError("Failed to load packages: ${error.message}")
                        loadingPanel.stopLoading()
                    }
                }
            )
        }.exceptionally { throwable ->
            SwingUtilities.invokeLater {
                showError("Error loading packages: ${throwable.message}")
                loadingPanel.stopLoading()
            }
            null
        }
    }
    
    private fun updatePackagesDisplay() {
        packagesPanel.removeAll()
        
        if (currentPackages.isEmpty()) {
            val noResultsLabel = JBLabel("No packages found").apply {
                horizontalAlignment = SwingConstants.CENTER
                foreground = UIUtil.getInactiveTextColor()
            }
            packagesPanel.add(noResultsLabel)
        } else {
            currentPackages.forEach { pkg ->
                val packageCard = McpPackageCard(project, pkg, packageInstaller) { packageId ->
                    // 刷新包状态
                    updatePackagesDisplay()
                }
                packagesPanel.add(packageCard)
                packagesPanel.add(Box.createVerticalStrut(10))
            }
        }
        
        packagesPanel.revalidate()
        packagesPanel.repaint()
    }
    
    private fun setupInstallListener() {
        project.messageBus.connect(disposable).subscribe(
            McpPackageInstallListener.TOPIC,
            object : McpPackageInstallListener {
                override fun onInstallStarted(packageId: String) {
                    SwingUtilities.invokeLater {
                        updatePackagesDisplay()
                    }
                }
                
                override fun onInstallProgress(packageId: String, progress: Int, message: String) {
                    // 可以在这里更新进度
                }
                
                override fun onInstallCompleted(packageId: String, success: Boolean, message: String) {
                    SwingUtilities.invokeLater {
                        updatePackagesDisplay()
                        if (success) {
                            showInfo("Package installed successfully: $packageId")
                        } else {
                            showError("Failed to install package: $message")
                        }
                    }
                }
                
                override fun onUninstallCompleted(packageId: String, success: Boolean) {
                    SwingUtilities.invokeLater {
                        updatePackagesDisplay()
                        if (success) {
                            showInfo("Package uninstalled successfully: $packageId")
                        } else {
                            showError("Failed to uninstall package: $packageId")
                        }
                    }
                }
            }
        )
    }
    
    private fun showError(message: String) {
        JOptionPane.showMessageDialog(
            this.contentPane,
            message,
            "Error",
            JOptionPane.ERROR_MESSAGE
        )
    }
    
    private fun showInfo(message: String) {
        JOptionPane.showMessageDialog(
            this.contentPane,
            message,
            "Information",
            JOptionPane.INFORMATION_MESSAGE
        )
    }
    
    public override fun dispose() {
        super.dispose()
    }
}
