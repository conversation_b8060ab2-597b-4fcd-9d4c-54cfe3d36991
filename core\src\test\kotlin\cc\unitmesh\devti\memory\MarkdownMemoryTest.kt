//package cc.unitmesh.devti.memory
//
//import com.intellij.testFramework.LightPlatformTestCase
//
///**
// * Markdown 记忆功能测试
// */
//class MarkdownMemoryTest : LightPlatformTestCase() {
//
//    private lateinit var memoryBankService: MemoryBankService
//
//    override fun setUp() {
//        super.setUp()
//        memoryBankService = MemoryBankService.getInstance(project)
//        memoryBankService.clearAllMemories()
//    }
//
//    fun testMarkdownContentStorage() {
//        // Given
//        val markdownContent = """
//            # 测试标题
//
//            这是一个**粗体**文本和*斜体*文本的示例。
//
//            ## 代码示例
//            ```kotlin
//            fun main() {
//                println("Hello, World!")
//            }
//            ```
//
//            ## 列表
//            - 项目 1
//            - 项目 2
//            - 项目 3
//
//            > 这是一个引用块
//        """.trimIndent()
//
//        // When
//        val memory = memoryBankService.addMemory(
//            title = "Markdown 测试",
//            content = markdownContent,
//            category = "test",
//            tags = listOf("markdown", "测试"),
//            importance = 3
//        )
//
//        // Then
//        assertNotNull("Memory should be created", memory)
//        assertEquals("Content should match", markdownContent, memory.content)
//
//        // Verify retrieval
//        val retrieved = memoryBankService.getAllMemories().find { it.id == memory.id }
//        assertNotNull("Memory should be retrievable", retrieved)
//        assertEquals("Retrieved content should match", markdownContent, retrieved!!.content)
//    }
//
//    fun testMarkdownSearchInContent() {
//        // Given
//        val markdownMemory1 = memoryBankService.addMemory(
//            "Kotlin 基础",
//            """
//            # Kotlin 编程语言
//
//            Kotlin 是一种**现代化**的编程语言。
//
//            ```kotlin
//            fun greet(name: String) = "Hello, $name!"
//            ```
//            """.trimIndent(),
//            "code",
//            listOf("kotlin", "编程"),
//            4
//        )
//
//        val markdownMemory2 = memoryBankService.addMemory(
//            "Java 基础",
//            """
//            # Java 编程语言
//
//            Java 是一种*面向对象*的编程语言。
//
//            ```java
//            public class Hello {
//                public static void main(String[] args) {
//                    System.out.println("Hello, World!");
//                }
//            }
//            ```
//            """.trimIndent(),
//            "code",
//            listOf("java", "编程"),
//            3
//        )
//
//        // When & Then
//        val kotlinResults = memoryBankService.searchMemories("Kotlin")
//        assertEquals("Should find Kotlin memory", 1, kotlinResults.size)
//        assertEquals("Should find correct memory", markdownMemory1.id, kotlinResults[0].id)
//
//        val codeResults = memoryBankService.searchMemories("编程语言")
//        assertEquals("Should find both memories", 2, codeResults.size)
//
//        val functionResults = memoryBankService.searchMemories("fun greet")
//        assertEquals("Should find Kotlin memory by code content", 1, functionResults.size)
//        assertEquals("Should find correct memory", markdownMemory1.id, functionResults[0].id)
//    }
//
//    fun testMarkdownWithSpecialCharacters() {
//        // Given
//        val complexMarkdown = """
//            # 特殊字符测试
//
//            ## 表格
//            | 列1 | 列2 | 列3 |
//            |-----|-----|-----|
//            | 数据1 | 数据2 | 数据3 |
//            | `代码` | **粗体** | *斜体* |
//
//            ## 链接和图片
//            [GitHub](https://github.com)
//            ![图片](https://example.com/image.png)
//
//            ## 任务列表
//            - [x] 已完成任务
//            - [ ] 未完成任务
//
//            ## 数学公式（如果支持）
//            $E = mc^2$
//
//            ## 转义字符
//            \*这不是斜体\*
//            \`这不是代码\`
//        """.trimIndent()
//
//        // When
//        val memory = memoryBankService.addMemory(
//            "复杂 Markdown 测试",
//            complexMarkdown,
//            "test",
//            listOf("markdown", "特殊字符", "表格"),
//            5
//        )
//
//        // Then
//        assertNotNull("Complex markdown memory should be created", memory)
//        assertEquals("Content should be preserved exactly", complexMarkdown, memory.content)
//
//        // Test search in complex content
//        val tableResults = memoryBankService.searchMemories("表格")
//        assertEquals("Should find memory by table content", 1, tableResults.size)
//
//        val linkResults = memoryBankService.searchMemories("GitHub")
//        assertEquals("Should find memory by link content", 1, linkResults.size)
//    }
//
//    fun testMarkdownCodeBlockSearch() {
//        // Given
//        val codeMemory = memoryBankService.addMemory(
//            "算法实现",
//            """
//            # 快速排序算法
//
//            ## Python 实现
//            ```python
//            def quicksort(arr):
//                if len(arr) <= 1:
//                    return arr
//                pivot = arr[len(arr) // 2]
//                left = [x for x in arr if x < pivot]
//                middle = [x for x in arr if x == pivot]
//                right = [x for x in arr if x > pivot]
//                return quicksort(left) + middle + quicksort(right)
//            ```
//
//            ## JavaScript 实现
//            ```javascript
//            function quickSort(arr) {
//                if (arr.length <= 1) return arr;
//                const pivot = arr[Math.floor(arr.length / 2)];
//                const left = arr.filter(x => x < pivot);
//                const middle = arr.filter(x => x === pivot);
//                const right = arr.filter(x => x > pivot);
//                return [...quickSort(left), ...middle, ...quickSort(right)];
//            }
//            ```
//            """.trimIndent(),
//            "code",
//            listOf("算法", "排序", "python", "javascript"),
//            5
//        )
//
//        // When & Then
//        val pythonResults = memoryBankService.searchMemories("def quicksort")
//        assertEquals("Should find memory by Python code", 1, pythonResults.size)
//
//        val jsResults = memoryBankService.searchMemories("function quickSort")
//        assertEquals("Should find memory by JavaScript code", 1, jsResults.size)
//
//        val algorithmResults = memoryBankService.searchMemories("pivot")
//        assertEquals("Should find memory by algorithm concept", 1, algorithmResults.size)
//    }
//
//    fun testMarkdownExportImport() {
//        // Given
//        val markdownMemories = listOf(
//            memoryBankService.addMemory(
//                "文档1",
//                "# 标题1\n\n这是**第一个**文档。",
//                "docs",
//                listOf("文档"),
//                3
//            ),
//            memoryBankService.addMemory(
//                "文档2",
//                "# 标题2\n\n这是*第二个*文档。",
//                "docs",
//                listOf("文档"),
//                4
//            )
//        )
//
//        // When
//        val exportedJson = memoryBankService.exportToJson()
//
//        // Clear and import
//        memoryBankService.clearAllMemories()
//        val imported = memoryBankService.importFromJson(exportedJson)
//
//        // Then
//        assertTrue("Import should succeed", imported)
//        assertEquals("Should have 2 memories after import", 2, memoryBankService.getAllMemories().size)
//
//        val importedMemories = memoryBankService.getAllMemories()
//        val doc1 = importedMemories.find { it.title == "文档1" }
//        val doc2 = importedMemories.find { it.title == "文档2" }
//
//        assertNotNull("Document 1 should be imported", doc1)
//        assertNotNull("Document 2 should be imported", doc2)
//
//        assertTrue("Doc1 content should contain markdown", doc1!!.content.contains("**第一个**"))
//        assertTrue("Doc2 content should contain markdown", doc2!!.content.contains("*第二个*"))
//    }
//
//    fun testInitializeSampleMemories() {
//        // Given - empty memory bank
//        assertEquals("Should start with no memories", 0, memoryBankService.getAllMemories().size)
//
//        // When
//        memoryBankService.initializeSampleMemories()
//
//        // Then
//        val memories = memoryBankService.getAllMemories()
//        assertTrue("Should have sample memories", memories.isNotEmpty())
//
//        // Verify sample memories contain markdown
//        val kotlinMemory = memories.find { it.title.contains("Kotlin") }
//        assertNotNull("Should have Kotlin memory", kotlinMemory)
//        assertTrue("Kotlin memory should contain markdown", kotlinMemory!!.content.contains("```kotlin"))
//
//        val gitMemory = memories.find { it.title.contains("Git") }
//        assertNotNull("Should have Git memory", gitMemory)
//        assertTrue("Git memory should contain code blocks", gitMemory!!.content.contains("```bash"))
//
//        // Test that calling again doesn't duplicate
//        val initialCount = memories.size
//        memoryBankService.initializeSampleMemories()
//        assertEquals("Should not duplicate sample memories", initialCount, memoryBankService.getAllMemories().size)
//    }
//}
