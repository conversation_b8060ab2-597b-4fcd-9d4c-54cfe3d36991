/edit_file

# File Editing Tool Guide

## 🚨 MANDATORY RULES
1. **NEVER provide isolated code snippets** - Always include complete structural context
2. **Markers preserve existing code** - Not for separating new code
3. **Pure new code MUST include enough context** - Class names, method signatures, etc.

## 📝 Usage Patterns

### Pattern 1: Pure New Code (Recommended)
```yaml
target_file: "src/main/java/MyClass.java"
instructions: "Add new method"
code_edit: |
  public class MyClass {
      public String newMethod() {
          return "new functionality";
      }
  }
```

### Pattern 2: Marker-Based Editing
```yaml
target_file: "src/main/java/MyClass.java"
instructions: "Add field at class beginning"
code_edit: |
  public class MyClass {
      private String newField = "value";
      
      // ... existing code ...
  }
```

## ✅ CORRECT Examples

### Complete Method with Changes
```yaml
code_edit: |
  public void bubbleSort(int[] arr) {
      // Outer loop for number of passes
      for (int i = 0; i < arr.length - 1; i++) {
          boolean swapped = false;
          // Inner loop for comparing adjacent elements
          for (int j = 0; j < arr.length - 1 - i; j++) {
              if (arr[j] > arr[j + 1]) {
                  int temp = arr[j];
                  arr[j] = arr[j + 1];
                  arr[j + 1] = temp;
                  swapped = true;
              }
          }
          // If no swaps occurred, terminate early
          if (!swapped) break;
      }
  }
```

## 🚫 FORBIDDEN Examples

### ❌ Isolated Code Fragments
```yaml
# NEVER DO THIS - No context:
code_edit: |
  // Inner loop for comparing adjacent elements
  for (int j = 0; j < arr.length - 1 - i; j++) {

# NEVER DO THIS - Orphaned statements:
code_edit: |
  // If no swaps occurred, terminate early
  if (!swapped) break;
```

### ❌ Markers with All New Code Between
```yaml
# NEVER DO THIS:
code_edit: |
  public class MyClass {
      // ... existing code ...
      
      String field1 = "a";     // All new code
      String field2 = "b";     // All new code
      void newMethod() {}      // All new code
      
      // ... existing code ...
  }
```

## 🔧 Supported Markers
- `// ... existing code ...`
- `// ... existing methods ...`
- `// ... existing fields ...`
- `// ... existing imports ...`

## ⚡ Quick Rules
1. **Include complete structure** - Class name, method signature, etc.
2. **No orphaned code lines** - Always provide context
3. **Markers only when needed** - For precise positioning
4. **One marker type per edit** - Don't mix different marker types

## 🎯 Remember
- System needs structural context to locate insertion points
- Isolated code fragments will fail
- When in doubt, include more context rather than less

## 注意事项
- 确保 `target_file` 路径正确
- `code_edit` 使用 `|` 符号保持代码格式
- 标记必须是注释形式且符合支持的模式
- 系统会自动处理代码中的注释、注解和字符串字面量





