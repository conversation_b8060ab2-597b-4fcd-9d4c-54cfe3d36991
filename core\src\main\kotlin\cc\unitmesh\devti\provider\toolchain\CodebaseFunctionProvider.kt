package cc.unitmesh.devti.provider.toolchain

import cc.unitmesh.devti.agent.tool.AgentTool
import cc.unitmesh.devti.agent.tool.search.RipgrepSearcher
import cc.unitmesh.devti.indexer.DomainDictService
import cc.unitmesh.devti.llms.LlmFactory
import cc.unitmesh.devti.template.GENIUS_CODE
import cc.unitmesh.devti.template.TemplateRender
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import kotlinx.coroutines.flow.collect

class CodebaseFunctionProvider : ToolchainFunctionProvider {
    private val templateRender = TemplateRender(GENIUS_CODE)
    private val template by lazy { templateRender.getTemplate("codebase-search.vm") }

    override suspend fun toolInfos(project: Project): List<AgentTool> = listOf(
        AgentTool(
            name = "codebase",
            description = "Optimize search keywords using domain-specific vocabulary and convert to search commands",
            example = """
                codebase("如何实现用户登录功能")
            """.trimIndent()
        )
    )

    override suspend fun funcNames(): List<String> = listOf("codebase")

    override suspend fun isApplicable(project: Project, funcName: String): Boolean = 
        funcName == "codebase"

    override suspend fun execute(
        project: Project, 
        prop: String, 
        args: List<Any>, 
        allVariables: Map<String, Any?>, 
        commandName: String
    ): Any {
        if (args.isEmpty()) return "Error: Missing query argument"
        
        val query = args[0].toString()
        val domainDictService = project.service<DomainDictService>()
        val dictContent = domainDictService.loadContent() ?: ""
        
        // 确定使用哪种搜索工具
        val searchTool = lookupSearchTool()
        
        return generateSearchCommand(project, query, dictContent, searchTool)
    }
    
    private suspend fun generateSearchCommand(
        project: Project,
        query: String, 
        dictContent: String, 
        searchTool: String
    ): String {
        val context = mapOf(
            "query" to query,
            "domainDict" to dictContent,
            "searchTool" to searchTool
        )
        
        val prompt = templateRender.renderTemplate(template, context)
        val llm = LlmFactory.create(project)
        
        val result = StringBuilder()
        llm.stream(prompt, systemPrompt = "You are a search query optimizer that specializes in improving search relevance using domain-specific terminology.").collect {
            result.append(it)
        }
        
        return result.toString().trim()
    }
    
    private fun lookupSearchTool(): String {
        val findRipgrepBinary = try {
            RipgrepSearcher.findRipgrepBinary()
        } catch (_: Exception) {
            null
        }

        return if (findRipgrepBinary != null) {
            "ripgrepSearch"
        } else {
            "localSearch"
        }
    }
}