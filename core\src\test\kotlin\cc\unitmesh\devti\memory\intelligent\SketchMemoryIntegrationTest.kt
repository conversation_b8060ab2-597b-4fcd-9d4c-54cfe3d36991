//package cc.unitmesh.devti.memory.intelligent
//
//import cc.unitmesh.devti.memory.MemoryBankService
//import cc.unitmesh.devti.sketch.SketchToolWindow
//import com.intellij.testFramework.LightPlatformTestCase
//import kotlinx.coroutines.runBlocking
//import javax.swing.JFrame
//
///**
// * SketchWindow记忆集成测试
// */
//class SketchMemoryIntegrationTest : LightPlatformTestCase() {
//
//    private lateinit var memoryIntegration: SketchMemoryIntegration
//    private lateinit var memoryBankService: MemoryBankService
//    private lateinit var facade: IntelligentMemoryFacade
//    private lateinit var monitor: MemoryProcessingMonitor
//
//    override fun setUp() {
//        super.setUp()
//        memoryIntegration = SketchMemoryIntegration.getInstance(project)
//        memoryBankService = MemoryBankService.getInstance(project)
//        facade = IntelligentMemoryFacade.getInstance(project)
//        monitor = MemoryProcessingMonitor.getInstance(project)
//
//        // 清空测试数据
//        memoryBankService.clearAllMemories()
//        memoryIntegration.clearConversationHistory()
//    }
//
//    fun testConversationRecording() {
//        // Given
//        val userMessage = "如何优化React组件的性能？"
//        val aiResponse = """
//            优化React组件性能的几个关键方法：
//
//            1. **使用React.memo**
//            ```jsx
//            const MyComponent = React.memo(({ data }) => {
//                return <div>{data.name}</div>;
//            });
//            ```
//
//            2. **使用useMemo和useCallback**
//            ```jsx
//            const expensiveValue = useMemo(() => {
//                return computeExpensiveValue(props.data);
//            }, [props.data]);
//            ```
//
//            3. **避免在render中创建对象**
//            4. **合理使用key属性**
//            5. **代码分割和懒加载**
//        """.trimIndent()
//
//        // When
//        memoryIntegration.recordConversation(userMessage, aiResponse)
//
//        // Then
//        val history = memoryIntegration.getConversationHistory()
//        assertEquals("应该记录对话", 1, history.size)
//
//        val conversation = history.first()
//        assertEquals("用户消息应该匹配", userMessage, conversation.userMessage)
//        assertEquals("AI响应应该匹配", aiResponse, conversation.aiResponse)
//        assertTrue("时间戳应该合理", conversation.timestamp > 0)
//    }
//
//    fun testBatchConversationProcessing() = runBlocking {
//        // Given
//        val conversations = listOf(
//            ConversationEntry("什么是Kotlin协程？", "Kotlin协程是轻量级线程...", System.currentTimeMillis()),
//            ConversationEntry("如何处理异常？", "在Kotlin中可以使用try-catch...", System.currentTimeMillis()),
//            ConversationEntry("数据库优化技巧？", "数据库优化包括索引优化...", System.currentTimeMillis())
//        )
//
//        conversations.forEach { entry ->
//            memoryIntegration.recordConversation(entry.userMessage, entry.aiResponse)
//        }
//
//        // When
//        val results = memoryIntegration.batchProcessConversationsAsMemories(conversations, "code")
//
//        // Then
//        assertEquals("应该处理所有对话", conversations.size, results.size)
//        assertTrue("所有处理都应该成功", results.all { it.success })
//
//        // 检查记忆银行中的记忆
//        val savedMemories = memoryBankService.getAllMemories()
//        assertTrue("应该保存记忆到记忆银行", savedMemories.isNotEmpty())
//    }
//
//    fun testImportantConversationDetection() {
//        // Given - 添加不同重要性的对话
//        val conversations = listOf(
//            ConversationEntry("你好", "你好！", System.currentTimeMillis()),
//            ConversationEntry(
//                "我遇到了一个严重的内存泄漏问题，应用在长时间运行后会崩溃",
//                "这是一个重要的问题。内存泄漏通常由以下原因造成：1. 未释放的监听器 2. 循环引用 3. 静态集合持有对象引用...",
//                System.currentTimeMillis()
//            ),
//            ConversationEntry("今天天气怎么样？", "我无法获取实时天气信息", System.currentTimeMillis()),
//            ConversationEntry(
//                "如何设计一个高并发的微服务架构？",
//                "设计高并发微服务架构需要考虑：1. 服务拆分策略 2. 负载均衡 3. 缓存设计 4. 数据库分片 5. 消息队列...",
//                System.currentTimeMillis()
//            )
//        )
//
//        conversations.forEach { entry ->
//            memoryIntegration.recordConversation(entry.userMessage, entry.aiResponse)
//        }
//
//        // When
//        val importantConversations = memoryIntegration.getImportantConversations(3)
//
//        // Then
//        assertTrue("应该识别出重要对话", importantConversations.isNotEmpty())
//        assertTrue("重要对话数量应该少于总对话", importantConversations.size < conversations.size)
//
//        // 检查是否包含预期的重要对话
//        val hasMemoryLeakConversation = importantConversations.any {
//            it.userMessage.contains("内存泄漏")
//        }
//        val hasMicroserviceConversation = importantConversations.any {
//            it.userMessage.contains("微服务架构")
//        }
//
//        assertTrue("应该识别内存泄漏对话为重要", hasMemoryLeakConversation)
//        assertTrue("应该识别微服务架构对话为重要", hasMicroserviceConversation)
//    }
//
//    fun testSketchWindowIntegration() {
//        // Given - 创建模拟的SketchWindow
//        val mockSketchWindow = createMockSketchWindow()
//
//        // When
//        memoryIntegration.integrateWithSketchWindow(mockSketchWindow)
//
//        // Then
//        // 验证集成是否成功（这里主要测试不抛异常）
//        assertTrue("集成应该成功", true)
//
//        // 清理
//        memoryIntegration.removeIntegration(mockSketchWindow)
//    }
//
//    fun testMemoryProcessingWithAISummary() = runBlocking {
//        // Given
//        val title = "React性能优化最佳实践"
//        val content = """
//            # React性能优化指南
//
//            ## 组件优化
//            1. 使用React.memo避免不必要的重渲染
//            2. 使用useMemo缓存计算结果
//            3. 使用useCallback缓存函数引用
//
//            ## 代码示例
//            ```jsx
//            const OptimizedComponent = React.memo(({ data, onClick }) => {
//                const processedData = useMemo(() => {
//                    return data.map(item => ({ ...item, processed: true }));
//                }, [data]);
//
//                const handleClick = useCallback((id) => {
//                    onClick(id);
//                }, [onClick]);
//
//                return (
//                    <div>
//                        {processedData.map(item => (
//                            <Item key={item.id} data={item} onClick={handleClick} />
//                        ))}
//                    </div>
//                );
//            });
//            ```
//
//            ## 性能监控
//            - 使用React DevTools Profiler
//            - 监控组件渲染次数
//            - 分析渲染时间
//        """.trimIndent()
//
//        // When
//        val result = facade.addMemoryWithContext(
//            title = title,
//            content = content,
//            source = "sketch_conversation",
//            context = mapOf(
//                "category" to "code",
//                "framework" to "react",
//                "type" to "optimization"
//            )
//        )
//
//        // Then
//        assertTrue("记忆添加应该成功", result.success)
//        assertNotNull("应该返回记忆ID", result.memoryId)
//
//        // 等待AI摘要生成
//        kotlinx.coroutines.delay(2000)
//
//        // 检查AI摘要
//        val aiSummary = facade.getMemorySummary(result.memoryId!!)
//        if (aiSummary != null) {
//            assertFalse("AI摘要不应为空", aiSummary.summary.isBlank())
//            assertTrue("应该识别为代码分类", aiSummary.suggestedCategory == "code")
//            assertTrue("应该包含React相关关键词",
//                aiSummary.keywords.any { it.contains("react", ignoreCase = true) })
//        }
//
//        // 检查记忆银行保存
//        val savedMemories = memoryBankService.getAllMemories()
//        val savedMemory = savedMemories.find { it.id == result.memoryId }
//        assertNotNull("应该保存到记忆银行", savedMemory)
//
//        if (savedMemory != null) {
//            assertTrue("保存的内容应该包含AI摘要", savedMemory.content.contains("AI生成摘要"))
//        }
//    }
//
//    fun testProcessingMonitorIntegration() = runBlocking {
//        // Given
//        val title = "测试监控集成"
//        val content = "这是一个用于测试处理监控集成的内容"
//
//        // When
//        val result = facade.addMemory(title, content)
//
//        // Then
//        assertTrue("记忆添加应该成功", result.success)
//
//        // 检查监控状态
//        val processingState = monitor.getProcessingState(result.memoryId!!)
//        if (processingState != null) {
//            assertEquals("记忆ID应该匹配", result.memoryId, processingState.memoryId)
//            assertEquals("标题应该匹配", title, processingState.title)
//            assertFalse("处理应该还在进行中", processingState.completed)
//        }
//
//        // 等待处理完成
//        kotlinx.coroutines.delay(3000)
//
//        // 检查统计信息
//        val stats = monitor.getProcessingStatistics()
//        assertTrue("应该有启动的处理", stats.totalInitiated.get() > 0)
//    }
//
//    fun testConversationListener() {
//        // Given - 创建模拟的SketchWindow
//        val mockSketchWindow = createMockSketchWindow()
//
//        // When
//        val listener = SketchConversationListener(project, mockSketchWindow)
//        listener.startListening()
//
//        // Then
//        assertTrue("监听器应该在运行", listener.isListening())
//
//        // 清理
//        listener.stopListening()
//        assertFalse("监听器应该已停止", listener.isListening())
//    }
//
//    private fun createMockSketchWindow(): SketchToolWindow {
//        // 创建一个简单的模拟SketchWindow
//        // 注意：这里需要根据实际的SketchToolWindow类来调整
//        return object : SketchToolWindow() {
//            init {
//                title = "Mock SketchWindow"
//                setSize(400, 300)
//            }
//        }
//    }
//}
