I need to optimize a search query for a codebase search using domain-specific terminology and convert it to a proper search command.

Original query: ${query}

Domain dictionary (CSV format with Chinese term, code term, and optional description):
```
${domainDict}
```

Search tool to use: ${searchTool}

Please:
1. Identify key concepts in the query
2. Replace or augment Chinese terms with their corresponding code terms from the dictionary
3. Format the result as a valid search command

Rules for formatting:
- For localSearch: Return a command in the format "/localSearch:optimized_query"
  - The query must be at least 4 characters long
  - If the query contains spaces, wrap it in triple backticks like: "/localSearch:```query with spaces```"

- For ripgrepSearch: Return a command in the format "/ripgrepSearch:optimized_query"
  - The query can be a regex pattern
  - If the query contains spaces, wrap it in triple backticks like: "/ripgrepSearch:```query with spaces```"

Return ONLY the formatted search command without explanations.
